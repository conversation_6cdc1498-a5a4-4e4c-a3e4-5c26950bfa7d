"""
Automatic Audio Device Detection and Management Module

This module provides automatic detection of system default audio input devices
and monitors for device changes to enable seamless switching between audio sources.
"""

import sys
import time
import threading
import pyaudio
from typing import Optional, Callable, List, Tuple, Dict

# Platform-specific imports
if sys.platform == "win32":
    try:
        from pycaw.pycaw import AudioUtilities, AudioEndpointVolume
        from comtypes import CLSCTX_ALL
        import psutil
        WINDOWS_AUDIO_AVAILABLE = True
    except ImportError:
        WINDOWS_AUDIO_AVAILABLE = False
        print("Warning: Windows audio monitoring libraries not available. Using fallback detection.")
else:
    WINDOWS_AUDIO_AVAILABLE = False


class AudioDeviceManager:
    """
    Manages automatic audio device detection and monitoring for seamless device switching.
    """
    
    def __init__(self, device_change_callback: Optional[Callable] = None):
        """
        Initialize the audio device manager.
        
        Args:
            device_change_callback: Callback function called when default device changes
        """
        self.audio = pyaudio.PyAudio()
        self.device_change_callback = device_change_callback
        self.monitoring_thread = None
        self.is_monitoring = False
        self.current_default_device = None
        self.last_device_check = 0
        self.device_check_interval = 2.0  # Check every 2 seconds
        
        # Device cache for performance
        self.device_cache = {}
        self.cache_timestamp = 0
        self.cache_duration = 5.0  # Cache devices for 5 seconds
        
    def get_system_default_input_device(self) -> Optional[int]:
        """
        Get the system's default audio input device index.
        
        Returns:
            Device index of the default input device, or None if not found
        """
        if sys.platform == "win32" and WINDOWS_AUDIO_AVAILABLE:
            return self._get_windows_default_input_device()
        else:
            return self._get_fallback_default_input_device()
    
    def _get_windows_default_input_device(self) -> Optional[int]:
        """
        Get default input device on Windows using pycaw.
        
        Returns:
            Device index or None if not found
        """
        try:
            # Get default audio input device from Windows
            devices = AudioUtilities.GetMicrophone()
            if not devices:
                return self._get_fallback_default_input_device()
            
            default_device = devices
            default_device_name = default_device.FriendlyName
            
            # Find corresponding PyAudio device index
            for i in range(self.audio.get_device_count()):
                try:
                    dev_info = self.audio.get_device_info_by_index(i)
                    if (dev_info['maxInputChannels'] > 0 and 
                        default_device_name.lower() in dev_info['name'].lower()):
                        print(f"Found Windows default input device: {i} - {dev_info['name']}")
                        return i
                except Exception:
                    continue
                    
            # If exact match not found, try partial matching
            for i in range(self.audio.get_device_count()):
                try:
                    dev_info = self.audio.get_device_info_by_index(i)
                    if (dev_info['maxInputChannels'] > 0 and 
                        any(word in dev_info['name'].lower() 
                            for word in default_device_name.lower().split())):
                        print(f"Found Windows default input device (partial match): {i} - {dev_info['name']}")
                        return i
                except Exception:
                    continue
                    
        except Exception as e:
            print(f"Error getting Windows default device: {e}")
            
        return self._get_fallback_default_input_device()
    
    def _get_fallback_default_input_device(self) -> Optional[int]:
        """
        Fallback method to get default input device.
        
        Returns:
            Device index or None if not found
        """
        try:
            # Try to get the default input device (usually index 0 or first available)
            default_device_info = self.audio.get_default_input_device_info()
            if default_device_info and default_device_info['maxInputChannels'] > 0:
                # Find the device index that matches the default device
                for i in range(self.audio.get_device_count()):
                    try:
                        dev_info = self.audio.get_device_info_by_index(i)
                        if (dev_info['name'] == default_device_info['name'] and
                            dev_info['maxInputChannels'] > 0):
                            print(f"Found fallback default input device: {i} - {dev_info['name']}")
                            return i
                    except Exception:
                        continue
        except Exception as e:
            print(f"Error getting default input device info: {e}")
        
        # Last resort: find first available input device
        for i in range(self.audio.get_device_count()):
            try:
                dev_info = self.audio.get_device_info_by_index(i)
                if dev_info['maxInputChannels'] > 0:
                    print(f"Using first available input device: {i} - {dev_info['name']}")
                    return i
            except Exception:
                continue
                
        print("No input devices found")
        return None
    
    def get_available_input_devices(self) -> List[Tuple[int, str]]:
        """
        Get list of available input devices with caching.
        
        Returns:
            List of (device_index, device_name) tuples
        """
        current_time = time.time()
        
        # Return cached devices if cache is still valid
        if (current_time - self.cache_timestamp < self.cache_duration and 
            self.device_cache):
            return self.device_cache.get('devices', [])
        
        devices = []
        try:
            for i in range(self.audio.get_device_count()):
                try:
                    dev = self.audio.get_device_info_by_index(i)
                    if dev['maxInputChannels'] > 0:
                        devices.append((i, dev['name']))
                except Exception:
                    continue
        except Exception as e:
            print(f"Error enumerating audio devices: {e}")
        
        # Update cache
        self.device_cache = {'devices': devices}
        self.cache_timestamp = current_time
        
        return devices
    
    def is_device_available(self, device_index: int) -> bool:
        """
        Check if a specific device is still available.
        
        Args:
            device_index: Device index to check
            
        Returns:
            True if device is available, False otherwise
        """
        try:
            dev_info = self.audio.get_device_info_by_index(device_index)
            return dev_info['maxInputChannels'] > 0
        except Exception:
            return False
    
    def start_monitoring(self):
        """Start monitoring for device changes."""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.current_default_device = self.get_system_default_input_device()
        
        self.monitoring_thread = threading.Thread(
            target=self._monitor_device_changes, 
            daemon=True
        )
        self.monitoring_thread.start()
        print("Audio device monitoring started")
    
    def stop_monitoring(self):
        """Stop monitoring for device changes."""
        self.is_monitoring = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1.0)
        print("Audio device monitoring stopped")
    
    def _monitor_device_changes(self):
        """Monitor for audio device changes in background thread."""
        while self.is_monitoring:
            try:
                current_time = time.time()
                
                # Check for device changes at specified interval
                if current_time - self.last_device_check >= self.device_check_interval:
                    new_default_device = self.get_system_default_input_device()
                    
                    # Check if default device changed
                    if (new_default_device != self.current_default_device and 
                        new_default_device is not None):
                        
                        old_device = self.current_default_device
                        self.current_default_device = new_default_device
                        
                        print(f"Default audio device changed: {old_device} -> {new_default_device}")
                        
                        # Notify callback about device change
                        if self.device_change_callback:
                            try:
                                self.device_change_callback(old_device, new_default_device)
                            except Exception as e:
                                print(f"Error in device change callback: {e}")
                    
                    self.last_device_check = current_time
                
                time.sleep(0.5)  # Check every 500ms for responsiveness
                
            except Exception as e:
                print(f"Error in device monitoring: {e}")
                time.sleep(1.0)  # Wait longer on error
    
    def get_device_info(self, device_index: int) -> Optional[Dict]:
        """
        Get detailed information about a specific device.
        
        Args:
            device_index: Device index
            
        Returns:
            Device information dictionary or None if not found
        """
        try:
            return self.audio.get_device_info_by_index(device_index)
        except Exception:
            return None
    
    def cleanup(self):
        """Clean up resources."""
        self.stop_monitoring()
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()
