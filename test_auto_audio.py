#!/usr/bin/env python3
"""
Test script for automatic audio device detection functionality.
"""

import time
import sys
from audio_device_manager import AudioDeviceManager

def test_device_detection():
    """Test automatic device detection."""
    print("Testing Automatic Audio Device Detection")
    print("=" * 50)
    
    def device_change_callback(old_device, new_device):
        print(f"Device changed: {old_device} -> {new_device}")
    
    # Create device manager
    manager = AudioDeviceManager(device_change_callback)
    
    # Test getting default device
    print("1. Testing default device detection...")
    default_device = manager.get_system_default_input_device()
    print(f"   Default input device: {default_device}")
    
    if default_device is not None:
        device_info = manager.get_device_info(default_device)
        if device_info:
            print(f"   Device name: {device_info['name']}")
            print(f"   Max input channels: {device_info['maxInputChannels']}")
    
    # Test getting available devices
    print("\n2. Testing available devices enumeration...")
    devices = manager.get_available_input_devices()
    print(f"   Found {len(devices)} input devices:")
    for idx, name in devices:
        marker = " (DEFAULT)" if idx == default_device else ""
        print(f"     {idx}: {name}{marker}")
    
    # Test device monitoring
    print("\n3. Testing device monitoring...")
    print("   Starting device monitoring for 10 seconds...")
    print("   Try connecting/disconnecting Bluetooth audio devices...")
    
    manager.start_monitoring()
    
    try:
        for i in range(10):
            time.sleep(1)
            current_default = manager.get_system_default_input_device()
            if current_default != default_device:
                print(f"   Default device changed to: {current_default}")
                default_device = current_default
            print(f"   Monitoring... {10-i} seconds remaining", end='\r')
        print("\n   Monitoring complete.")
    except KeyboardInterrupt:
        print("\n   Monitoring interrupted by user.")
    
    # Clean up
    manager.cleanup()
    print("\n4. Test completed successfully!")

if __name__ == "__main__":
    try:
        test_device_detection()
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
