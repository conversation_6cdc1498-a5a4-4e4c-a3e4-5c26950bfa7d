import pyaudio
import wave
import speech_recognition as sr
import threading
import time
import os
import io
import struct
from concurrent.futures import ThreadPoolExecutor

# Configuration for audio recording - OPTIMIZED FOR ACCURACY AND RELIABILITY
FORMAT = pyaudio.paInt16  # Audio format (16-bit PCM)
CHANNELS = 1              # Number of audio channels (1 for mono)
RATE = 16000              # Reduced sample rate for better STT accuracy (Google STT optimized for 16kHz)
CHUNK = 1024              # Chunk size for processing
SILENCE_THRESHOLD = 300   # Increased threshold for better noise rejection
SILENT_CHUNKS_TO_STOP = int(1.5 * RATE / CHUNK) # Longer silence to ensure complete questions
MIN_AUDIO_LENGTH = int(1.0 * RATE / CHUNK)  # Minimum 1 second to avoid noise
MAX_RECORDING_CHUNKS = int(45 * RATE / CHUNK)  # Maximum 45 seconds for longer questions
TEMP_AUDIO_FILENAME = "temp_interview_audio.wav"

# Performance optimization settings
ENABLE_IN_MEMORY_PROCESSING = True  # Use in-memory processing instead of file I/O
CONCURRENT_PROCESSING = False       # Disable concurrent processing to avoid race conditions
ENABLE_NOISE_REDUCTION = True       # Enable ambient noise adjustment
ADAPTIVE_THRESHOLD = True           # Enable adaptive silence threshold

class AudioHandler:
    def __init__(self, app_callback, selected_device_index_callback):
        self.audio = pyaudio.PyAudio()
        self.stream = None
        self.is_listening = False
        self.recognizer = sr.Recognizer()

        # IMPROVED: Better recognition settings for accuracy
        self.recognizer.energy_threshold = 4000  # Higher threshold for better noise rejection
        self.recognizer.dynamic_energy_threshold = True  # Enable adaptive threshold
        self.recognizer.pause_threshold = 1.0  # Longer pause to capture complete questions
        self.recognizer.phrase_threshold = 0.3  # Better phrase detection
        self.recognizer.operation_timeout = 15.0  # Longer timeout for better reliability

        self.app_callback = app_callback
        self.selected_device_index_callback = selected_device_index_callback
        self.frames = []
        self.recording_thread = None
        self.continuous_mode = True  # Enable continuous listening mode
        self.current_audio_frames = []  # Current audio segment being recorded

        # Adaptive threshold variables
        self.adaptive_silence_threshold = SILENCE_THRESHOLD
        self.volume_history = []
        self.ambient_noise_level = 0
        self.noise_calibrated = False

        # PERFORMANCE OPTIMIZATION: Thread pool for concurrent processing
        if CONCURRENT_PROCESSING:
            self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="audio_proc")
        else:
            self.executor = None

        # Track processing segments to avoid duplicate processing
        self.processing_segments = set()

        # Audio quality tracking
        self.last_successful_transcription_time = 0
        self.failed_transcription_count = 0

    def get_available_input_devices(self):
        devices = []
        for i in range(self.audio.get_device_count()):
            dev = self.audio.get_device_info_by_index(i)
            if dev['maxInputChannels'] > 0:
                devices.append((i, dev['name']))
        return devices

    def calibrate_ambient_noise(self, duration=2.0):
        """Calibrate ambient noise level for better voice detection"""
        if not self.stream:
            return False

        print("Calibrating ambient noise level...")
        self.app_callback("status", "Status: Calibrating ambient noise...")

        calibration_chunks = int(duration * RATE / CHUNK)
        noise_samples = []

        try:
            for _ in range(calibration_chunks):
                data = self.stream.read(CHUNK, exception_on_overflow=False)
                audio_data = struct.unpack(str(CHUNK) + 'h', data)
                volume = sum(abs(sample) for sample in audio_data) / CHUNK
                noise_samples.append(volume)

            # Calculate ambient noise level (average + some margin)
            self.ambient_noise_level = sum(noise_samples) / len(noise_samples)
            self.adaptive_silence_threshold = max(SILENCE_THRESHOLD, self.ambient_noise_level * 2.5)
            self.noise_calibrated = True

            print(f"Ambient noise level: {self.ambient_noise_level:.1f}")
            print(f"Adaptive threshold set to: {self.adaptive_silence_threshold:.1f}")
            self.app_callback("status", "Status: Noise calibration complete")
            return True

        except Exception as e:
            print(f"Error during noise calibration: {e}")
            self.app_callback("status", "Status: Noise calibration failed")
            return False

    def update_adaptive_threshold(self, current_volume):
        """Update adaptive silence threshold based on recent audio"""
        if not ADAPTIVE_THRESHOLD:
            return

        # Keep a rolling window of volume levels
        self.volume_history.append(current_volume)
        if len(self.volume_history) > 50:  # Keep last 50 samples
            self.volume_history.pop(0)

        # Update threshold based on recent volume patterns
        if len(self.volume_history) >= 10:
            avg_volume = sum(self.volume_history) / len(self.volume_history)
            # Adjust threshold to be above average background noise
            self.adaptive_silence_threshold = max(SILENCE_THRESHOLD, avg_volume * 1.8)

    def _record_audio_thread(self):
        device_index = self.selected_device_index_callback()
        if device_index is None:
            self.app_callback("error", "No audio input device selected or available.")
            self.is_listening = False # Ensure listening stops if no device
            self.app_callback("status", "Status: Error - No device.")
            return

        try:
            self.stream = self.audio.open(format=FORMAT,
                                         channels=CHANNELS,
                                         rate=RATE,
                                         input=True,
                                         frames_per_buffer=CHUNK,
                                         input_device_index=device_index)
        except Exception as e:
            error_msg = f"Failed to open audio stream: {e}. Try another device or check permissions."
            print(error_msg)
            self.app_callback("error", error_msg)
            self.is_listening = False
            self.app_callback("status", f"Status: Error - {e}")
            return

        self.frames = []
        self.current_audio_frames = []
        silent_chunks_count = 0
        has_speech = False
        total_chunks = 0
        print(f"Audio stream opened on device {device_index}. Recording...")

        # Calibrate ambient noise if enabled
        if ENABLE_NOISE_REDUCTION and not self.noise_calibrated:
            if self.calibrate_ambient_noise():
                print("Noise calibration successful")
            else:
                print("Noise calibration failed, using default threshold")

        self.app_callback("status", "Status: Listening...")

        while self.is_listening:
            try:
                data = self.stream.read(CHUNK, exception_on_overflow=False)
                self.frames.append(data)
                self.current_audio_frames.append(data)
                total_chunks += 1

                # Prevent memory overflow - clear old frames if too many
                if len(self.frames) > MAX_RECORDING_CHUNKS:
                    self.frames = self.frames[-MAX_RECORDING_CHUNKS//2:]  # Keep only recent half

                # IMPROVED: Enhanced voice activity detection with adaptive threshold
                audio_data = struct.unpack(str(CHUNK) + 'h', data)
                volume = sum(abs(sample) for sample in audio_data) / CHUNK

                # Update adaptive threshold
                self.update_adaptive_threshold(volume)

                # Use adaptive threshold for better accuracy
                current_threshold = self.adaptive_silence_threshold if ADAPTIVE_THRESHOLD else SILENCE_THRESHOLD

                # Debug: Print volume levels for troubleshooting (less frequent)
                if total_chunks % 20 == 0:
                    print(f"VAD Debug - Volume: {volume:.1f}, Threshold: {current_threshold:.1f}, HasSpeech: {has_speech}, SilentChunks: {silent_chunks_count}, CurrentFrames: {len(self.current_audio_frames)}")

                # Improved voice activity detection
                if volume > current_threshold:
                    silent_chunks_count = 0
                    has_speech = True
                    # Reset failed transcription count on successful voice detection
                    if self.failed_transcription_count > 0:
                        self.failed_transcription_count = max(0, self.failed_transcription_count - 1)
                else:
                    silent_chunks_count += 1

                # OPTIMIZED: Process audio when we detect silence after speech
                if (self.continuous_mode and has_speech and
                    silent_chunks_count >= SILENT_CHUNKS_TO_STOP and
                    len(self.current_audio_frames) >= MIN_AUDIO_LENGTH):

                    # Process current audio segment in background using thread pool
                    audio_to_process = b''.join(self.current_audio_frames)
                    segment_id = int(time.time() * 1000)  # Unique segment identifier

                    if segment_id not in self.processing_segments:
                        self.processing_segments.add(segment_id)

                        # Always use direct processing for better reliability
                        processing_thread = threading.Thread(
                            target=self._process_audio_segment_optimized,
                            args=(audio_to_process, segment_id),
                            daemon=True
                        )
                        processing_thread.start()

                    # Reset for next segment
                    self.current_audio_frames = []
                    has_speech = False
                    silent_chunks_count = 0

            except IOError as e:
                if e.errno == pyaudio.paInputOverflowed:
                    print("Input overflowed. Dropping frame(s).")
                else:
                    print(f"IOError during recording: {e}")
                    self.app_callback("error", f"IOError: {e}")
                    self.is_listening = False # Stop on other IOErrors
                    break
            except Exception as e:
                print(f"Unexpected error during recording: {e}")
                self.app_callback("error", f"Recording Error: {e}")
                self.is_listening = False
                break

        # Cleanup stream resources before processing
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
            self.stream = None
        print("Audio stream closed.")

        if self.frames and not self.is_listening: # Process only when explicitly stopped
            self.app_callback("status", "Status: Processing captured audio...")
            self._process_recorded_audio(b''.join(self.frames))
            self.frames = [] # Clear frames after processing
        elif not self.frames and not self.is_listening:
            print("No frames recorded to process.")
            self.app_callback("status", "Status: Idle. No audio captured.")
        # If is_listening is still true here, it means an error occurred and loop broke
        elif self.is_listening: # Should not happen if logic is correct
            self.app_callback("status", "Status: Idle (recording error occurred).")

    def _process_audio_segment_optimized(self, audio_data_bytes, segment_id):
        """IMPROVED: Process audio segment with better quality checks and error handling"""
        if not audio_data_bytes:
            return

        try:
            start_time = time.time()

            # Check audio quality before processing
            if len(audio_data_bytes) < MIN_AUDIO_LENGTH * CHUNK * 2:  # 2 bytes per sample
                print(f"Segment {segment_id}: Audio too short, skipping")
                return

            if ENABLE_IN_MEMORY_PROCESSING:
                # Use in-memory processing for better performance
                audio_buffer = io.BytesIO()
                with wave.open(audio_buffer, 'wb') as wf:
                    wf.setnchannels(CHANNELS)
                    wf.setsampwidth(self.audio.get_sample_size(FORMAT))
                    wf.setframerate(RATE)
                    wf.writeframes(audio_data_bytes)

                audio_buffer.seek(0)
                with sr.AudioFile(audio_buffer) as source:
                    # Apply noise reduction if enabled
                    if ENABLE_NOISE_REDUCTION:
                        self.recognizer.adjust_for_ambient_noise(source, duration=0.2)
                    audio_for_stt = self.recognizer.record(source)
            else:
                # Fallback to file-based processing
                segment_filename = f"temp_segment_{segment_id}.wav"
                with wave.open(segment_filename, 'wb') as wf:
                    wf.setnchannels(CHANNELS)
                    wf.setsampwidth(self.audio.get_sample_size(FORMAT))
                    wf.setframerate(RATE)
                    wf.writeframes(audio_data_bytes)

                with sr.AudioFile(segment_filename) as source:
                    if ENABLE_NOISE_REDUCTION:
                        self.recognizer.adjust_for_ambient_noise(source, duration=0.2)
                    audio_for_stt = self.recognizer.record(source)

            # Transcribe audio segment with improved settings
            print(f"Segment {segment_id}: Attempting STT with language 'en-US'...")
            text = self.recognizer.recognize_google(
                audio_for_stt,
                language="en-US",
                show_all=False  # Get only the best result
            )
            print(f"Segment {segment_id}: STT successful.")

            processing_time = time.time() - start_time

            if text.strip():  # Only process non-empty transcriptions
                # Validate transcription quality
                if self._validate_transcription(text):
                    print(f"IMPROVED Transcription ({processing_time:.3f}s): {text}")
                    self.last_successful_transcription_time = time.time()
                    self.failed_transcription_count = 0
                    self.app_callback("transcription", text)
                else:
                    print(f"Segment {segment_id}: Transcription failed quality check: {text}")
                    self.failed_transcription_count += 1

        except sr.UnknownValueError:
            print(f"Segment {segment_id}: Google Speech Recognition could not understand audio.")
            self.failed_transcription_count += 1
            # Don't send unclear audio to AI to avoid confusion
        except sr.RequestError as e:
            print(f"Segment {segment_id}: STT Request Error: {e}")
            self.failed_transcription_count += 1
            self.app_callback("error", f"STT Error (Segment {segment_id}): {e}")
        except Exception as e:
            print(f"Segment {segment_id}: Error processing audio: {e}")
            self.failed_transcription_count += 1
            self.app_callback("error", f"Audio Processing Error (Segment {segment_id}): {e}")
        finally:
            # Clean up resources
            if not ENABLE_IN_MEMORY_PROCESSING and 'segment_filename' in locals():
                try:
                    if os.path.exists(segment_filename):
                        os.remove(segment_filename)
                except OSError:
                    pass

            # Remove from processing set
            self.processing_segments.discard(segment_id)

    def _validate_transcription(self, text):
        """Validate transcription quality to avoid sending poor quality text to AI"""
        if not text or len(text.strip()) < 3:
            return False

        # Check for common transcription errors
        text_lower = text.lower().strip()

        # Filter out very short or nonsensical transcriptions
        if len(text_lower) < 5:
            return False

        # Filter out repeated characters or words (common STT errors)
        words = text_lower.split()
        if len(words) > 1:
            # Check for excessive repetition
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.5:  # More than 50% repetition
                return False

        # Filter out transcriptions that are likely noise
        noise_patterns = ['uh', 'um', 'ah', 'er', 'hmm', 'mhm', 'yeah yeah yeah']
        if any(pattern in text_lower for pattern in noise_patterns) and len(words) <= 3:
            return False

        return True

    def _process_audio_segment(self, audio_data_bytes):
        """Legacy method - kept for compatibility"""
        segment_id = int(time.time() * 1000)
        self._process_audio_segment_optimized(audio_data_bytes, segment_id)

    def _process_recorded_audio(self, audio_data_bytes):
        if not audio_data_bytes:
            self.app_callback("transcription", "[No audio data to process]")
            return

        try:
            # Save to a temporary WAV file for speech_recognition library
            with wave.open(TEMP_AUDIO_FILENAME, 'wb') as wf:
                wf.setnchannels(CHANNELS)
                wf.setsampwidth(self.audio.get_sample_size(FORMAT))
                wf.setframerate(RATE)
                wf.writeframes(audio_data_bytes)
            print(f"Temporary audio saved to {TEMP_AUDIO_FILENAME}")

            with sr.AudioFile(TEMP_AUDIO_FILENAME) as source:
                # recognizer.adjust_for_ambient_noise(source, duration=0.5) # Optional: adjust for noise
                audio_for_stt = self.recognizer.record(source)  # Read the entire audio file

            print("Transcribing audio with language 'en-US'...")
            self.app_callback("status", "Status: Transcribing...")
            text = self.recognizer.recognize_google(audio_for_stt, language="en-US")
            print(f"Full Transcription: {text}")
            self.app_callback("transcription", text)
            self.app_callback("status", "Status: Transcription complete.") # Update status

        except sr.UnknownValueError:
            print("Full Audio: Google Speech Recognition could not understand audio")
            self.app_callback("transcription", "[AI_ERROR: Could not understand audio]")
            self.app_callback("status", "Status: Error - Unclear audio.")
        except sr.RequestError as e:
            error_msg = f"STT Request Error: {e}"
            print(f"Full Audio: Could not request results from Google Speech Recognition service; {e}")
            self.app_callback("transcription", f"[AI_ERROR: STT Service Error]")
            self.app_callback("error", error_msg)
            self.app_callback("status", "Status: Error - STT service unavailable.")
        except Exception as e:
            error_msg = f"Error during full audio transcription: {e}"
            print(error_msg)
            self.app_callback("transcription", f"[AI_ERROR: Transcription Failed]")
            self.app_callback("error", error_msg)
            self.app_callback("status", "Status: Error - Transcription process failed.")
        finally:
            if os.path.exists(TEMP_AUDIO_FILENAME):
                try:
                    os.remove(TEMP_AUDIO_FILENAME)
                    print(f"Temporary file {TEMP_AUDIO_FILENAME} deleted.")
                except OSError as e:
                    print(f"Error deleting temporary file {TEMP_AUDIO_FILENAME}: {e}")
            # Status update after processing is done
            if not self.is_listening: # Ensure this is called only if stop was intended
                self.app_callback("status", "Status: Idle. Ready to listen.")

    def start_listening(self):
        if not self.is_listening:
            self.is_listening = True
            self.frames = [] # Clear frames from previous session
            self.current_audio_frames = [] # Clear current segment frames
            self.app_callback("status", "Status: Initializing audio...")
            self.recording_thread = threading.Thread(target=self._record_audio_thread)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            print("Audio listening thread started in continuous mode.")
        else:
            print("Already listening.")

    def stop_listening(self):
        if self.is_listening:
            self.is_listening = False # Signal the recording thread to stop
            print("Stop listening signal sent. Waiting for thread to finish current recording and process...")
            self.app_callback("status", "Status: Stopping and processing audio...")
            # The thread will see is_listening is False, exit its loop, and then process.
            # We might want to join the thread here if synchronous stop is critical,
            # but that could freeze UI if processing takes time.
            # For now, let it finish and callback.
        else:
            print("Not currently listening.")
            self.app_callback("status", "Status: Idle.") # Update status if stop is called when not listening

    def close_audio(self):
        print("Attempting to close PyAudio...")
        if self.is_listening:
            self.stop_listening() # Ensure listening stops before closing
            if self.recording_thread and self.recording_thread.is_alive():
                print("Waiting for recording thread to complete before closing PyAudio...")
                self.recording_thread.join(timeout=5.0) # Wait for thread to finish
                if self.recording_thread.is_alive():
                    print("Warning: Recording thread did not complete in time.")

        # OPTIMIZATION: Clean up thread pool executor
        if self.executor:
            print("Shutting down thread pool executor...")
            self.executor.shutdown(wait=True, timeout=3.0)
            self.executor = None

        if self.stream and self.stream.is_active(): # Should be closed by thread, but as a safeguard
            self.stream.stop_stream()
            self.stream.close()
            self.stream = None
            print("Stream closed directly by close_audio.")

        self.audio.terminate()
        print("PyAudio terminated.")

# Example usage (for testing audio_handler.py directly)
def dummy_app_callback_test(event_type, data):
    print(f"APP CALLBACK - Type: {event_type}, Data: {data}")

def dummy_selected_device_callback_test():
    # For testing, print devices and pick the first one if available
    p = pyaudio.PyAudio()
    default_device_index = None
    for i in range(p.get_device_count()):
        dev = p.get_device_info_by_index(i)
        if dev['maxInputChannels'] > 0:
            print(f"Found input device: {i} - {dev['name']}")
            if default_device_index is None: # Pick the first one found
                 default_device_index = i
    p.terminate()
    if default_device_index is not None:
        print(f"Using device index: {default_device_index} for test.")
        return default_device_index
    print("No input device found for test.")
    return 0 # Fallback to default, might fail if no device 0

if __name__ == '__main__':
    print("Running AudioHandler direct test...")
    handler = AudioHandler(dummy_app_callback_test, dummy_selected_device_callback_test)

    available_devices = handler.get_available_input_devices()
    if not available_devices:
        print("No audio input devices found. Exiting test.")
    else:
        print("Available audio input devices:")
        for index, name in available_devices:
            print(f"  {index}: {name}")

        print("\nStarting listening for 7 seconds (test)...")
        handler.start_listening()
        # Simulate listening duration
        start_time = time.time()
        while time.time() - start_time < 7:
            if not handler.is_listening and handler.recording_thread and not handler.recording_thread.is_alive():
                print("Recording thread stopped unexpectedly during test.")
                break
            time.sleep(0.1)

        print("\nStopping listening (test)...")
        handler.stop_listening()

        # Wait for processing to complete, thread should exit
        if handler.recording_thread and handler.recording_thread.is_alive():
            print("Waiting for recording thread to finish processing...")
            handler.recording_thread.join(timeout=10) # Increased timeout for STT
            if handler.recording_thread.is_alive():
                 print("Warning: Recording thread did not finish in time during test cleanup.")

        print("\nClosing audio (test)...")
        handler.close_audio()
        print("\nAudio handler direct test finished.")

    # Note on differentiating interviewer's voice:
    # This remains a significant challenge with a single audio input.
    # - System audio loopback (e.g., VB-Cable on Windows, BlackHole on macOS) is the most reliable way
    #   to capture only the computer's output (i.e., the interviewer in an online meeting).
    #   The user would need to configure their system and select this loopback device in the app.
    # - Speaker diarization is complex and computationally intensive for real-time use.
    # - The current implementation relies on the user selecting the correct input device.
    #   If they select their microphone, it will capture their voice too.
    #   If they select a loopback device piping meeting audio, it should primarily get the interviewer.