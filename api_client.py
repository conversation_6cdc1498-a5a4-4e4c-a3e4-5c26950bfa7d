import requests
import json
import time
import hashlib
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
try:
    from config import GOOGLE_GEMINI_API_KEY
except ImportError:
    print("Error: Could not import GOOGLE_GEMINI_API_KEY from config.py.")
    print("Please ensure config.py exists and contains your API key.")
    GOOGLE_GEMINI_API_KEY = None

# Using Gemini 1.5 Flash model
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={GOOGLE_GEMINI_API_KEY}"

class APIClient:
    def __init__(self):
        if not GOOGLE_GEMINI_API_KEY:
            raise ValueError("Google Gemini API Key is not configured. Please check config.py.")

        # Initialize optimized HTTP session with connection pooling and keep-alive
        self.session = requests.Session()

        # ULTRA-OPTIMIZED HTTP adapter for sub-1-second performance
        retry_strategy = Retry(
            total=1,  # Minimal retries for maximum speed
            backoff_factor=0.05,  # Ultra-fast backoff
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(
            pool_connections=10,  # Increased connection pool size
            pool_maxsize=20,      # Larger max connections in pool
            max_retries=retry_strategy,
            pool_block=False     # Don't block when pool is full
        )
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)

        # OPTIMIZED headers for maximum speed
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Connection': 'keep-alive',
            'Keep-Alive': 'timeout=60, max=200',  # Longer keep-alive for better reuse
            'Accept-Encoding': 'gzip, deflate',   # Enable compression
            'User-Agent': 'InterviewAssistant/1.0'  # Custom user agent
        })

        # Response cache for identical questions (performance optimization)
        self.response_cache = {}
        self.cache_max_size = 50  # Limit cache size
        self.cache_ttl = 300  # 5 minutes TTL

        # Frontend development interview system prompt
        self.system_prompt = """
You are a job candidate in a frontend development interview.

🟢 Your Role:
- Answer all questions like a real frontend developer
- Do not mention that you are an AI
- Always answer like a real person with real experience

🧠 How to Answer:
- Keep your answers short, clear, and complete
- Use simple English – no fancy words
- Give examples if needed
- If asked for code, write correct working code with comments
- Explain your code in simple steps
- Share best practices if possible

🛠️ Technology Skills:
You are an expert in:
- Angular
- React
- JavaScript
- TypeScript
- HTML and CSS
You also know full-stack using:
- MERN (MongoDB, Express, React, Node.js)
- MEAN (MongoDB, Express, Angular, Node.js)

🧩 Understanding the Question:
Before answering, check which framework or tech the question is about:
1. If it says Angular or React, answer using that framework.
2. If it uses special terms like "hooks" (React) or "directives" (Angular), answer using that tech.
3. If the question is common to both (like "component"), explain it in both Angular and React, and compare.

📘 Some tech examples:
- Angular: directives, services, modules, pipes, decorators, NgModule, two-way binding
- React: hooks, JSX, props, state, functional/class components, Redux
- Vue (optional): computed properties, watchers, Vue instance

💬 HR Questions:
If asked about soft skills or personal questions, answer like a real person.
Examples:
- "How do you handle pressure?" → "I stay calm by breaking tasks into small steps and taking short breaks."
- "Tell me about yourself" → Give a short intro with skills and experience.

🖼️ Image Questions:
If an image is given (like UI screenshot or code):
- Describe what you see
- Tell what tech or feature is used
- Answer the question clearly

❓ If the question is not clear:
- You can ask for clarification
- Or make a reasonable assumption and mention it

💡 Always explain your thought process if needed.
"""

        # Pre-warm connection with a lightweight request
        self._warm_connection()

    def _warm_connection(self):
        """Pre-warm the HTTP connection to reduce first request latency."""
        try:
            # Make a minimal request to establish connection
            warm_payload = {
                "contents": [{"parts": [{"text": "Hi"}]}],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 10
                }
            }
            # Use a very short timeout for warming
            self.session.post(GEMINI_API_URL, data=json.dumps(warm_payload), timeout=2)
        except:
            # Ignore warming failures - connection will be established on first real request
            pass

    def _get_cache_key(self, text):
        """Generate context-aware cache key for the given text"""
        # Normalize text for better matching
        normalized_text = text.lower().strip()

        # Remove common filler words that don't affect meaning
        filler_words = ['um', 'uh', 'er', 'ah', 'like', 'you know', 'so', 'well']
        words = normalized_text.split()
        filtered_words = [word for word in words if word not in filler_words]

        # Create key from filtered, normalized text
        key_text = ' '.join(filtered_words)
        return hashlib.md5(key_text.encode()).hexdigest()

    def _is_cache_valid(self, timestamp):
        """Check if cache entry is still valid"""
        return time.time() - timestamp < self.cache_ttl

    def _clean_cache(self):
        """Remove expired entries and maintain cache size limit"""
        current_time = time.time()
        # Remove expired entries
        expired_keys = [k for k, (_, timestamp) in self.response_cache.items()
                       if current_time - timestamp >= self.cache_ttl]
        for key in expired_keys:
            del self.response_cache[key]

        # Maintain size limit (remove oldest entries)
        if len(self.response_cache) > self.cache_max_size:
            sorted_items = sorted(self.response_cache.items(),
                                key=lambda x: x[1][1])  # Sort by timestamp
            items_to_remove = len(self.response_cache) - self.cache_max_size
            for i in range(items_to_remove):
                del self.response_cache[sorted_items[i][0]]

    def get_ai_response(self, transcribed_text, streaming_callback=None):
        """Sends transcribed text to Google Gemini API and gets a response with optimizations."""
        if not transcribed_text:
            return "No transcription provided to AI."

        # Performance optimization: Check cache first
        cache_key = self._get_cache_key(transcribed_text)
        if cache_key in self.response_cache:
            cached_response, timestamp = self.response_cache[cache_key]
            if self._is_cache_valid(timestamp):
                print(f"Cache hit for question: {transcribed_text[:50]}...")
                # If streaming callback provided, simulate streaming for cached response
                if streaming_callback:
                    self._simulate_streaming(cached_response, streaming_callback)
                return cached_response

        # Clean cache periodically
        self._clean_cache()

        # Enhanced prompt for better accuracy
        interview_prompt = f"""{self.system_prompt}

INTERVIEWER'S QUESTION: "{transcribed_text}"

INSTRUCTIONS: Analyze the question carefully. Provide a technically accurate, contextually relevant answer as a job candidate. Ensure you understand what is being asked before responding.

YOUR ANSWER (2-3 sentences, technically accurate):"""
        print(f"DEBUG: Sending to AI - Question: {transcribed_text}") # Log the question

        # Optimized payload for accuracy and speed
        payload = {
            "contents": [{"parts": [{"text": interview_prompt}]}],
            "generationConfig": {
                "temperature": 0.2,      # Slightly higher for better accuracy
                "topK": 3,               # Allow some variation for better responses
                "topP": 0.7,             # Better balance for accuracy
                "maxOutputTokens": 180,  # Slightly more tokens for complete answers
                "candidateCount": 1      # Single candidate for speed
            }
        }

        try:
            # Performance timing for monitoring
            start_time = time.time()
            print(f"Sending to Gemini: {transcribed_text[:50]}...")

            # Balanced timeout for reliability and speed
            response = self.session.post(GEMINI_API_URL, data=json.dumps(payload), timeout=3.0)
            response.raise_for_status()

            response_time = time.time() - start_time
            print(f"API response time: {response_time:.3f}s")

            response_json = response.json()

            # Enhanced response extraction with validation
            if 'candidates' in response_json and response_json['candidates']:
                candidate = response_json['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content'] and candidate['content']['parts']:
                    ai_text = candidate['content']['parts'][0]['text'].strip()

                    # Validate response quality
                    is_valid, validation_message = self._validate_response_quality(transcribed_text, ai_text)

                    if is_valid:
                        print(f"✓ Validated AI Response: {ai_text}")

                        # Cache the validated response
                        self.response_cache[cache_key] = (ai_text, time.time())

                        # If streaming callback provided, stream the response AFTER caching
                        if streaming_callback:
                            self._simulate_streaming(ai_text, streaming_callback)

                        return ai_text
                    else:
                        print(f"❌ Response validation failed: {validation_message}")
                        print(f"❌ Invalid response: {ai_text}")

                        # Try to regenerate response with more specific prompt
                        return self._regenerate_response(transcribed_text, ai_text, validation_message)
                elif 'finishReason' in candidate and candidate['finishReason'] != 'STOP':
                    return f"[AI Error: Generation stopped due to {candidate['finishReason']}]"

            # Optimized error handling
            error_message = response_json.get('error', {}).get('message', 'Unknown error')
            if 'promptFeedback' in response_json and 'blockReason' in response_json['promptFeedback']:
                 error_message = f"Content blocked: {response_json['promptFeedback']['blockReason']}"
            print(f"Gemini API error: {error_message}")
            return f"[AI Error: {error_message}]"

        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error: {http_err}")
            return f"[API HTTP Error: {http_err}]"
        except requests.exceptions.Timeout:
            print("Request timeout - API took too long to respond")
            return "[API Timeout: Response took too long]"
        except requests.exceptions.RequestException as req_err:
            print(f"Request error: {req_err}")
            return f"[API Request Error: {req_err}]"
        except Exception as e:
            print(f"Unexpected error calling Gemini API: {e}")
            return f"[Unexpected API Error: {e}]"

    def _simulate_streaming(self, full_response, callback):
        """Simulate streaming by sending response word by word to callback."""
        import threading
        import time

        def stream_words():
            words = full_response.split()
            current_text = ""

            for i, word in enumerate(words):
                current_text += word + " "
                # Call the callback with partial response
                try:
                    callback(current_text.strip())
                except:
                    # If callback fails, continue streaming
                    pass

                # ULTRA-FAST streaming for immediate visibility
                if i < 3:  # First 3 words instantly
                    time.sleep(0.01)  # 10ms for immediate start
                elif i < 8:  # Next 5 words very fast
                    time.sleep(0.015)  # 15ms
                else:
                    time.sleep(0.025)  # 25ms for remaining words

        # Run streaming in separate thread to not block
        threading.Thread(target=stream_words, daemon=True).start()

    def _validate_response_quality(self, question, response):
        """Validate that the response is appropriate for the frontend development question"""
        if not response or len(response.strip()) < 10:
            return False, "Response too short"

        question_lower = question.lower()
        response_lower = response.lower()

        # Check if response mentions being an AI (should not happen with new prompt)
        ai_mentions = [' i am an ai', ' as an ai', ' i\'m an ai', ' artificial intelligence', ' language model', 'i am ai', 'as ai']
        if any(mention in response_lower for mention in ai_mentions):
            return False, "Response mentions being an AI"

        # Frontend-specific concept validation
        frontend_checks = [
            # React vs Angular confusion
            {
                'question_keywords': ['react', 'jsx', 'hooks', 'props', 'state'],
                'wrong_response_keywords': ['angular', 'directive', 'service', 'module', 'ngmodule'],
                'correct_keywords': ['component', 'jsx', 'hooks', 'props', 'state', 'react'],
                'error': "Confused React concepts with Angular"
            },
            # Angular vs React confusion
            {
                'question_keywords': ['angular', 'directive', 'service', 'module', 'ngmodule'],
                'wrong_response_keywords': ['react', 'jsx', 'hooks', 'props'],
                'correct_keywords': ['component', 'directive', 'service', 'module', 'angular'],
                'error': "Confused Angular concepts with React"
            },
            # JavaScript hoisting vs web hosting
            {
                'question_keywords': ['hoisting', 'hoist'],
                'wrong_response_keywords': ['hosting', 'server', 'website', 'domain'],
                'correct_keywords': ['variable', 'function', 'scope', 'declaration', 'javascript'],
                'error': "Confused JavaScript hoisting with web hosting"
            },
            # Closures validation
            {
                'question_keywords': ['closure', 'closures'],
                'wrong_response_keywords': ['enclosure', 'closing', 'close'],
                'correct_keywords': ['function', 'scope', 'variable', 'outer', 'inner'],
                'error': "Incorrect closure explanation"
            },
            # TypeScript validation
            {
                'question_keywords': ['typescript', 'interface', 'type'],
                'wrong_response_keywords': ['javascript only'],
                'correct_keywords': ['type', 'interface', 'typescript', 'static'],
                'error': "Incorrect TypeScript explanation"
            }
        ]

        for check in frontend_checks:
            # Check if question is about this concept
            if any(keyword in question_lower for keyword in check['question_keywords']):
                # Check if response contains wrong keywords
                wrong_count = sum(1 for keyword in check['wrong_response_keywords'] if keyword in response_lower)
                if wrong_count > 0:
                    # Verify it has enough correct keywords to compensate
                    correct_count = sum(1 for keyword in check['correct_keywords'] if keyword in response_lower)
                    if correct_count < 2:  # Need at least 2 correct keywords
                        return False, check['error']

        # Check for reasonable length (not too short for technical questions)
        if any(tech in question_lower for tech in ['react', 'angular', 'javascript', 'typescript', 'component']):
            if len(response.split()) < 15:  # Technical answers should be substantial
                return False, "Technical response too brief"

        return True, "Response validated"

    def _regenerate_response(self, question, invalid_response, error_reason):
        """Regenerate response with more specific instructions to fix errors"""
        print(f"🔄 Regenerating response due to: {error_reason}")

        # Create more specific prompt based on the error
        correction_prompt = f"""{self.system_prompt}

CRITICAL: The previous response was incorrect due to: {error_reason}

INTERVIEWER'S QUESTION: "{question}"

PREVIOUS INCORRECT RESPONSE: "{invalid_response}"

INSTRUCTIONS:
1. Carefully read the question again
2. Identify the specific concept being asked about
3. Provide a technically accurate response that directly addresses the question
4. Do NOT repeat the previous error

CORRECTED ANSWER (2-3 sentences, technically accurate):"""

        payload = {
            "contents": [{"parts": [{"text": correction_prompt}]}],
            "generationConfig": {
                "temperature": 0.1,      # Lower temperature for more focused response
                "topK": 1,               # Single choice for consistency
                "topP": 0.5,             # More focused
                "maxOutputTokens": 180,
                "candidateCount": 1
            }
        }

        try:
            response = self.session.post(GEMINI_API_URL, data=json.dumps(payload), timeout=3.0)
            response.raise_for_status()
            response_json = response.json()

            if 'candidates' in response_json and response_json['candidates']:
                candidate = response_json['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content'] and candidate['content']['parts']:
                    corrected_text = candidate['content']['parts'][0]['text'].strip()

                    # Validate the corrected response
                    is_valid, validation_message = self._validate_response_quality(question, corrected_text)

                    if is_valid:
                        print(f"✓ Corrected response validated: {corrected_text}")
                        return corrected_text
                    else:
                        print(f"❌ Corrected response still invalid: {validation_message}")
                        # Return a safe fallback response
                        return "I need to think more carefully about that question. Could you please rephrase it or provide more context?"

            return "I apologize, but I need more clarification on that question to provide an accurate answer."

        except Exception as e:
            print(f"Error during response regeneration: {e}")
            return "I apologize, but I'm having difficulty processing that question right now."

    def close(self):
        """Clean up resources"""
        if hasattr(self, 'session'):
            self.session.close()

# Example usage (for testing api_client.py directly)
if __name__ == '__main__':
    if GOOGLE_GEMINI_API_KEY:
        client = APIClient()
        sample_transcription = "Can you tell me about your experience with Python and web development?"
        print(f"Testing AI response for: {sample_transcription}")
        response = client.get_ai_response(sample_transcription)
        print(f"\nFormatted AI Response:\n{response}")

        sample_transcription_2 = "What are your salary expectations?"
        print(f"\nTesting AI response for: {sample_transcription_2}")
        response_2 = client.get_ai_response(sample_transcription_2)
        print(f"\nFormatted AI Response:\n{response_2}")
    else:
        print("Cannot run APIClient test because GOOGLE_GEMINI_API_KEY is not set in config.py.")