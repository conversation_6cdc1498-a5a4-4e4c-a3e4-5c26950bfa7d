# Speech AI Interview Assistant - IMPROVED VERSION

A real-time speech recognition and AI-powered interview assistant application with enhanced accuracy, better response quality, and improved speech recognition capabilities.

## 🚀 Recent Improvements

### ✅ Fixed Speech Recognition Issues
- **Enhanced Voice Activity Detection**: Improved threshold settings and adaptive noise calibration
- **Better Audio Processing**: Optimized sample rate (16kHz) for Google STT accuracy
- **Noise Reduction**: Added ambient noise adjustment and filtering
- **Quality Validation**: Transcription quality checks to avoid sending poor audio to AI

### ✅ Fixed Response Accuracy Issues
- **Context-Aware AI**: Enhanced system prompt with technical accuracy validation
- **Concept Confusion Prevention**: Specific checks for hoisting vs hosting, closures, etc.
- **Response Validation**: Automatic quality checks and regeneration for incorrect responses
- **Better Caching**: Improved cache key generation with filler word removal

### ✅ Fixed Audio Processing Issues
- **Complete Question Capture**: Longer silence detection to ensure full questions
- **Improved Error Handling**: Better handling of audio stream errors and timeouts
- **Reliability Improvements**: Disabled concurrent processing to prevent race conditions

### ✅ Code Cleanup
- **Removed Unnecessary Files**: Cleaned up multiple redundant test files
- **Proper Dependencies**: Created comprehensive requirements.txt
- **Better Logging**: Enhanced debug output and error reporting

## 🎯 Key Features

- **Accurate Speech Recognition**: Optimized for interview environments
- **Intelligent AI Responses**: Context-aware answers with technical accuracy validation
- **Stealth Mode**: Window transparency and screen capture hiding
- **Real-time Processing**: Live transcription and response generation
- **Quality Assurance**: Built-in validation to prevent incorrect responses

## 📋 Requirements

- Python 3.7+
- Audio input device (microphone or virtual audio cable)
- Google Gemini API key
- Internet connection for speech recognition and AI services

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd speech-ai-interview
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure API key**
   Edit `config.py` and add your Google Gemini API key:
   ```python
   GOOGLE_GEMINI_API_KEY = "your_api_key_here"
   ```

4. **Test the installation**
   ```bash
   python test_comprehensive.py
   ```

5. **Run the application**
   ```bash
   python main.py
   ```