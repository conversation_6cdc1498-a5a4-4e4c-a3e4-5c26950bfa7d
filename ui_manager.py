import tkinter as tk
import threading
import time

class UIManager:
    def __init__(self, transcription_widget, ai_response_widget, status_bar_widget):
        self.transcription_text_widget = transcription_widget
        self.ai_response_text_widget = ai_response_widget
        self.status_bar_widget = status_bar_widget
        self.typing_active = False

    def update_transcription(self, text):
        """Appends new transcribed text to the transcription display area."""
        self.transcription_text_widget.config(state=tk.NORMAL)
        self.transcription_text_widget.insert(tk.END, text + "\n")
        self.transcription_text_widget.see(tk.END) # Scroll to the end
        self.transcription_text_widget.config(state=tk.DISABLED)

    def update_ai_response(self, text):
        """Displays AI response with fast progressive typing effect."""
        # Stop any existing typing animation
        self.typing_active = False
        time.sleep(0.02)  # Brief pause to ensure previous animation stops

        # Clear and start new response
        self.ai_response_text_widget.config(state=tk.NORMAL)
        self.ai_response_text_widget.delete(1.0, tk.END)
        self.ai_response_text_widget.config(state=tk.DISABLED)

        # Start streaming response in a separate thread
        threading.Thread(target=self._stream_response, args=(text,), daemon=True).start()

    def _stream_response(self, text):
        """OPTIMIZED: Stream response with ultra-fast word display for immediate visibility."""
        self.typing_active = True

        # Split the text to handle Q&A format
        if "Q) " in text and "Answer:" in text:
            parts = text.split("Answer:")
            question_part = parts[0].strip()  # "Q) question text"
            answer_part = parts[1].strip() if len(parts) > 1 else ""

            # Show question immediately
            self.ai_response_text_widget.after(0, self._update_text_widget, question_part + "\n\nAnswer:\n")
            time.sleep(0.01)  # Minimal pause for immediate display

            # ULTRA-FAST streaming: Stream answer word by word
            words = answer_part.split()
            current_answer = ""

            for i, word in enumerate(words):
                if not self.typing_active:
                    break

                current_answer += word + " "
                full_text = question_part + "\n\nAnswer:\n" + current_answer

                # Update UI immediately for each word
                self.ai_response_text_widget.after(0, self._update_text_widget, full_text)

                # OPTIMIZED: Ultra-fast streaming with variable delays
                if i < 3:  # First 3 words instantly
                    time.sleep(0.005)  # 5ms for immediate start
                elif i < 8:  # Next 5 words very fast
                    time.sleep(0.01)   # 10ms
                else:
                    time.sleep(0.015)  # 15ms for remaining words
        else:
            # Fallback: show text immediately
            self.ai_response_text_widget.after(0, self._update_text_widget, text)

        self.typing_active = False

    def stream_word_update(self, partial_text):
        """Update UI with partial response for real-time streaming."""
        self.ai_response_text_widget.after(0, self._update_text_widget, partial_text)

    def _update_text_widget(self, text):
        """Thread-safe method to update text widget."""
        self.ai_response_text_widget.config(state=tk.NORMAL)
        self.ai_response_text_widget.delete(1.0, tk.END)
        self.ai_response_text_widget.insert(tk.END, text)
        self.ai_response_text_widget.see(tk.END)
        self.ai_response_text_widget.config(state=tk.DISABLED)

    def update_status_bar(self, text):
        """Updates the text in the status bar."""
        self.status_bar_widget.config(text=text)

    def clear_transcription(self):
        """Clears the transcription display area."""
        self.transcription_text_widget.config(state=tk.NORMAL)
        self.transcription_text_widget.delete(1.0, tk.END)
        self.transcription_text_widget.config(state=tk.DISABLED)

    def clear_ai_response(self):
        """Clears the AI response display area and stops typing animation."""
        self.typing_active = False  # Stop any ongoing typing animation
        self.ai_response_text_widget.config(state=tk.NORMAL)
        self.ai_response_text_widget.delete(1.0, tk.END)
        self.ai_response_text_widget.insert(tk.END, "🤖 AI interview assistant ready...\n\nI'll provide helpful responses as an expert software developer during your technical interview.") # Enhanced placeholder
        self.ai_response_text_widget.config(state=tk.DISABLED)

# This UIManager is quite basic. For more complex applications, you might consider
# using queues for thread-safe UI updates from background threads if direct
# widget manipulation from threads becomes problematic, though Tkinter's mainloop
# usually handles calls from other threads if they are scheduled correctly (e.g. using `after`).
# However, for simplicity in this example, direct calls are made, assuming they are
# managed by the main application logic to occur in a safe manner (e.g., via callbacks
# that are ultimately run in the main Tkinter thread).