#!/usr/bin/env python3
"""
Test script for correct technical answers.
Tests that responses are technically accurate.
"""

import time
from api_client import APIClient

def test_correct_answers():
    """Test correct technical answers."""
    print("=== Correct Technical Answers Test ===\n")
    
    try:
        # Initialize API client
        print("Initializing API client...")
        client = APIClient()
        print("✓ API client initialized successfully\n")
        
        # Clear cache for fresh responses
        client.response_cache.clear()
        
        # Test questions with expected concepts
        test_cases = [
            {
                "question": "What is hoisting in JavaScript?",
                "expected_concepts": ["variables", "move", "top", "before", "declare"],
                "wrong_concepts": ["website", "online", "hosting", "server"]
            },
            {
                "question": "What is hosting in web development?",
                "expected_concepts": ["website", "online", "server", "internet"],
                "wrong_concepts": ["variables", "hoisting", "move", "top"]
            },
            {
                "question": "What are JavaScript closures?",
                "expected_concepts": ["function", "remember", "variables", "scope"],
                "wrong_concepts": ["website", "hosting", "online"]
            },
            {
                "question": "What is event handling in JavaScript?",
                "expected_concepts": ["click", "user", "action", "function"],
                "wrong_concepts": ["hosting", "hoisting", "variables"]
            }
        ]
        
        print("Testing Technical Accuracy:")
        print("=" * 60)
        
        for i, test_case in enumerate(test_cases, 1):
            question = test_case["question"]
            expected = test_case["expected_concepts"]
            wrong = test_case["wrong_concepts"]
            
            print(f"\nTest {i}/4: {question}")
            print("-" * 40)
            
            # Get response
            start_time = time.time()
            response = client.get_ai_response(question)
            response_time = time.time() - start_time
            
            # Check if response is valid
            is_error = response.startswith("[") and response.endswith("]")
            
            if not is_error:
                print(f"Response ({response_time:.2f}s):")
                print(f"'{response}'")
                
                # Check for expected concepts
                response_lower = response.lower()
                found_expected = []
                found_wrong = []
                
                for concept in expected:
                    if concept.lower() in response_lower:
                        found_expected.append(concept)
                
                for concept in wrong:
                    if concept.lower() in response_lower:
                        found_wrong.append(concept)
                
                # Analyze accuracy
                expected_score = len(found_expected) / len(expected) * 100
                
                print(f"\n  ✓ Expected concepts found: {found_expected}")
                print(f"  ✓ Accuracy score: {expected_score:.1f}%")
                
                if found_wrong:
                    print(f"  ❌ Wrong concepts found: {found_wrong}")
                    print(f"  ⚠️  TECHNICAL ERROR DETECTED!")
                else:
                    print(f"  ✓ No wrong concepts detected")
                
                if expected_score >= 50 and not found_wrong:
                    print(f"  🎉 TECHNICALLY CORRECT!")
                else:
                    print(f"  ❌ NEEDS IMPROVEMENT!")
                
            else:
                print(f"❌ Error response: {response}")
            
            # Small delay between requests
            time.sleep(0.5)
        
        print(f"\n{'='*60}")
        print("TECHNICAL ACCURACY TEST SUMMARY:")
        print(f"{'='*60}")
        print("✓ Responses should be technically accurate")
        print("✓ Hoisting ≠ Hosting (different concepts)")
        print("✓ Use correct technical terminology")
        print("✓ Avoid concept confusion")
        print("\n🎯 Technical accuracy with simple English!")
        
        # Clean up
        client.close()
        print("\n✓ Test completed successfully")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_correct_answers()
