#!/usr/bin/env python3
"""
Comprehensive test script for the improved Speech AI Interview Application.
Tests speech recognition accuracy, response quality, and technical correctness.
"""

import time
import sys
import os
from api_client import APIClient
from audio_handler import AudioHandler

def test_api_response_accuracy():
    """Test API response accuracy and validation"""
    print("=== API Response Accuracy Test ===\n")
    
    try:
        client = APIClient()
        client.response_cache.clear()  # Clear cache for fresh responses
        
        test_cases = [
            {
                "question": "What is hoisting in JavaScript?",
                "expected_keywords": ["variable", "function", "scope", "declaration", "top"],
                "forbidden_keywords": ["hosting", "server", "website", "domain"],
                "description": "JavaScript hoisting concept"
            },
            {
                "question": "What is web hosting?", 
                "expected_keywords": ["server", "website", "internet", "domain", "online"],
                "forbidden_keywords": ["hoisting", "variable", "scope", "declaration"],
                "description": "Web hosting service"
            },
            {
                "question": "What are JavaScript closures?",
                "expected_keywords": ["function", "scope", "variable", "outer", "access"],
                "forbidden_keywords": ["enclosure", "closing", "close"],
                "description": "JavaScript closures concept"
            },
            {
                "question": "What is a REST API?",
                "expected_keywords": ["http", "get", "post", "resource", "stateless"],
                "forbidden_keywords": ["soap", "graphql"],
                "description": "REST API architecture"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            question = test_case["question"]
            expected = test_case["expected_keywords"]
            forbidden = test_case["forbidden_keywords"]
            description = test_case["description"]
            
            print(f"Test {i}/4: {description}")
            print(f"Question: {question}")
            print("-" * 60)
            
            start_time = time.time()
            response = client.get_ai_response(question)
            response_time = time.time() - start_time
            
            if response and not response.startswith("["):
                print(f"Response ({response_time:.2f}s): {response}")
                
                # Check response quality
                response_lower = response.lower()
                found_expected = [kw for kw in expected if kw in response_lower]
                found_forbidden = [kw for kw in forbidden if kw in response_lower]
                
                accuracy_score = len(found_expected) / len(expected) * 100
                has_forbidden = len(found_forbidden) > 0
                
                print(f"✓ Expected keywords found: {found_expected}")
                print(f"✓ Accuracy score: {accuracy_score:.1f}%")
                
                if found_forbidden:
                    print(f"❌ Forbidden keywords found: {found_forbidden}")
                    print(f"❌ CONCEPT CONFUSION DETECTED!")
                    test_result = "FAILED"
                elif accuracy_score >= 60:
                    print(f"✅ TECHNICALLY CORRECT!")
                    test_result = "PASSED"
                else:
                    print(f"⚠️ NEEDS IMPROVEMENT (low accuracy)")
                    test_result = "PARTIAL"
                    
                results.append({
                    "test": description,
                    "result": test_result,
                    "accuracy": accuracy_score,
                    "has_confusion": has_forbidden,
                    "response_time": response_time
                })
                
            else:
                print(f"❌ Error response: {response}")
                results.append({
                    "test": description,
                    "result": "ERROR",
                    "accuracy": 0,
                    "has_confusion": False,
                    "response_time": response_time
                })
            
            print()
            time.sleep(0.5)  # Brief pause between tests
        
        # Summary
        print("=" * 60)
        print("TEST SUMMARY:")
        print("=" * 60)
        
        passed = sum(1 for r in results if r["result"] == "PASSED")
        failed = sum(1 for r in results if r["result"] == "FAILED")
        errors = sum(1 for r in results if r["result"] == "ERROR")
        avg_accuracy = sum(r["accuracy"] for r in results) / len(results)
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        
        print(f"✅ Passed: {passed}/{len(results)}")
        print(f"❌ Failed: {failed}/{len(results)}")
        print(f"🔧 Errors: {errors}/{len(results)}")
        print(f"📊 Average Accuracy: {avg_accuracy:.1f}%")
        print(f"⏱️ Average Response Time: {avg_response_time:.2f}s")
        
        if failed == 0 and errors == 0:
            print("\n🎉 ALL TESTS PASSED! Response accuracy is excellent.")
        elif failed > 0:
            print(f"\n⚠️ {failed} tests failed due to concept confusion.")
        
        client.close()
        return results
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return []

def test_audio_handler_basic():
    """Test basic audio handler functionality"""
    print("\n=== Audio Handler Basic Test ===\n")
    
    try:
        def dummy_callback(event_type, data):
            print(f"Audio Event: {event_type} - {data}")
        
        def dummy_device_callback():
            return 0  # Default device
        
        handler = AudioHandler(dummy_callback, dummy_device_callback)
        
        # Test device enumeration
        devices = handler.get_available_input_devices()
        print(f"Available audio devices: {len(devices)}")
        for idx, name in devices[:3]:  # Show first 3 devices
            print(f"  {idx}: {name}")
        
        # Test validation method
        test_transcriptions = [
            ("What is JavaScript?", True),
            ("um uh er", False),
            ("a", False),
            ("hello hello hello hello", False),
            ("Can you explain closures?", True)
        ]
        
        print("\nTranscription validation tests:")
        for text, expected in test_transcriptions:
            result = handler._validate_transcription(text)
            status = "✅" if result == expected else "❌"
            print(f"{status} '{text}' -> {result} (expected {expected})")
        
        handler.close_audio()
        print("\n✅ Audio handler basic tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Audio handler test failed: {e}")
        return False

def main():
    """Run comprehensive tests"""
    print("🧪 Starting Comprehensive Speech AI Interview Application Tests")
    print("=" * 70)
    
    # Test API response accuracy
    api_results = test_api_response_accuracy()
    
    # Test audio handler basics
    audio_result = test_audio_handler_basic()
    
    # Overall summary
    print("\n" + "=" * 70)
    print("🏁 COMPREHENSIVE TEST SUMMARY")
    print("=" * 70)
    
    if api_results:
        api_passed = sum(1 for r in api_results if r["result"] == "PASSED")
        print(f"🤖 API Response Tests: {api_passed}/{len(api_results)} passed")
    else:
        print("🤖 API Response Tests: FAILED TO RUN")
    
    print(f"🎤 Audio Handler Tests: {'PASSED' if audio_result else 'FAILED'}")
    
    if api_results and audio_result:
        api_success_rate = sum(1 for r in api_results if r["result"] == "PASSED") / len(api_results)
        if api_success_rate >= 0.75:
            print("\n🎉 OVERALL RESULT: EXCELLENT - Application is working well!")
        elif api_success_rate >= 0.5:
            print("\n⚠️ OVERALL RESULT: GOOD - Minor improvements needed")
        else:
            print("\n❌ OVERALL RESULT: NEEDS WORK - Significant issues detected")
    else:
        print("\n❌ OVERALL RESULT: CRITICAL ISSUES - Core functionality broken")
    
    print("\n💡 Next steps:")
    print("1. Run the main application: python main.py")
    print("2. Test with real audio input")
    print("3. Verify speech recognition accuracy")
    print("4. Check response relevance during actual interviews")

if __name__ == "__main__":
    main()
