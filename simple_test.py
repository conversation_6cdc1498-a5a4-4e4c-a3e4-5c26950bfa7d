import sys
print("Python version:", sys.version)

try:
    import pyaudio
    print("PyAudio imported successfully")
    
    p = pyaudio.PyAudio()
    print(f"Found {p.get_device_count()} audio devices")
    
    # Try to get default input device
    try:
        default_info = p.get_default_input_device_info()
        print(f"Default input device: {default_info['name']}")
    except Exception as e:
        print(f"Error getting default device: {e}")
    
    p.terminate()
    
except Exception as e:
    print(f"Error with PyAudio: {e}")

try:
    import pycaw
    print("pycaw imported successfully")
except Exception as e:
    print(f"Error importing pycaw: {e}")

print("Basic test completed")
